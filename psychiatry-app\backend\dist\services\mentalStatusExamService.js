"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MentalStatusExamService = void 0;
const database_1 = require("../utils/database");
class MentalStatusExamService {
    static async createMentalStatusExam(data, createdBy, auditContext) {
        try {
            const patient = await database_1.prisma.patient.findUnique({
                where: { id: data.patientId },
            });
            if (!patient) {
                return {
                    success: false,
                    error: 'Patient not found',
                };
            }
            const examiner = await database_1.prisma.user.findUnique({
                where: { id: data.examinerId },
            });
            if (!examiner) {
                return {
                    success: false,
                    error: 'Examiner not found',
                };
            }
            const examDate = new Date(data.examDate);
            const mentalStatusExam = await database_1.prisma.mentalStatusExam.create({
                data: {
                    patientId: data.patientId,
                    examDate,
                    examinerId: data.examinerId,
                    appearance_grooming: data.appearance_grooming || null,
                    appearance_dress: data.appearance_dress || null,
                    appearance_hygiene: data.appearance_hygiene || null,
                    behavior_eye_contact: data.behavior_eye_contact || null,
                    behavior_motor: data.behavior_motor || null,
                    behavior_cooperation: data.behavior_cooperation || null,
                    speech_rate: data.speech_rate || null,
                    speech_volume: data.speech_volume || null,
                    speech_tone: data.speech_tone || null,
                    speech_fluency: data.speech_fluency || null,
                    mood_reported: data.mood_reported || null,
                    mood_observed: data.mood_observed || null,
                    affect_type: data.affect_type || null,
                    affect_range: data.affect_range || null,
                    affect_appropriateness: data.affect_appropriateness || null,
                    thought_process: data.thought_process || null,
                    thought_organization: data.thought_organization || null,
                    thought_flow: data.thought_flow || null,
                    thought_content: data.thought_content || null,
                    delusions: data.delusions || false,
                    delusion_type: data.delusion_type || null,
                    obsessions: data.obsessions || false,
                    compulsions: data.compulsions || false,
                    phobias: data.phobias || false,
                    hallucinations: data.hallucinations || false,
                    hallucination_type: data.hallucination_type || null,
                    illusions: data.illusions || false,
                    depersonalization: data.depersonalization || false,
                    derealization: data.derealization || false,
                    orientation_person: data.orientation_person !== false,
                    orientation_place: data.orientation_place !== false,
                    orientation_time: data.orientation_time !== false,
                    orientation_situation: data.orientation_situation !== false,
                    attention_span: data.attention_span || null,
                    concentration: data.concentration || null,
                    memory_immediate: data.memory_immediate || null,
                    memory_recent: data.memory_recent || null,
                    memory_remote: data.memory_remote || null,
                    abstract_thinking: data.abstract_thinking || null,
                    insight_level: data.insight_level || null,
                    insight_description: data.insight_description || null,
                    judgment_level: data.judgment_level || null,
                    judgment_description: data.judgment_description || null,
                    suicidal_ideation: data.suicidal_ideation || null,
                    suicidal_risk: data.suicidal_risk || null,
                    homicidal_ideation: data.homicidal_ideation || null,
                    homicidal_risk: data.homicidal_risk || null,
                    clinical_notes: data.clinical_notes || null,
                    recommendations: data.recommendations || null,
                    followup_needed: data.followup_needed || false,
                },
                include: {
                    patient: {
                        select: {
                            id: true,
                            patientId: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    examiner: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            role: true,
                        },
                    },
                },
            });
            return {
                success: true,
                data: mentalStatusExam,
                message: 'Mental status exam created successfully',
            };
        }
        catch (error) {
            console.error('Error creating mental status exam:', error);
            return {
                success: false,
                error: 'Failed to create mental status exam',
            };
        }
    }
    static async getAllMentalStatusExams(filters, page = 1, limit = 10) {
        try {
            const skip = (page - 1) * limit;
            const where = {};
            if (filters.patientId) {
                where.patientId = filters.patientId;
            }
            if (filters.examinerId) {
                where.examinerId = filters.examinerId;
            }
            const [mentalStatusExams, total] = await Promise.all([
                database_1.prisma.mentalStatusExam.findMany({
                    where,
                    include: {
                        patient: {
                            select: {
                                id: true,
                                patientId: true,
                                firstName: true,
                                lastName: true,
                            },
                        },
                        examiner: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                role: true,
                            },
                        },
                    },
                    orderBy: { examDate: 'desc' },
                    skip,
                    take: limit,
                }),
                database_1.prisma.mentalStatusExam.count({ where }),
            ]);
            return {
                success: true,
                data: mentalStatusExams,
                pagination: {
                    page,
                    limit,
                    total,
                    pages: Math.ceil(total / limit),
                },
            };
        }
        catch (error) {
            console.error('Error fetching mental status exams:', error);
            return {
                success: false,
                error: 'Failed to fetch mental status exams',
                data: [],
                pagination: { page, limit, total: 0, pages: 0 },
            };
        }
    }
    static async getMentalStatusExamById(id) {
        try {
            const mentalStatusExam = await database_1.prisma.mentalStatusExam.findUnique({
                where: { id },
                include: {
                    patient: {
                        select: {
                            id: true,
                            patientId: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    examiner: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            role: true,
                        },
                    },
                },
            });
            if (!mentalStatusExam) {
                return {
                    success: false,
                    error: 'Mental status exam not found',
                };
            }
            return {
                success: true,
                data: mentalStatusExam,
            };
        }
        catch (error) {
            console.error('Error fetching mental status exam:', error);
            return {
                success: false,
                error: 'Failed to fetch mental status exam',
            };
        }
    }
    static async getMentalStatusExamsByPatient(patientId, limit = 10) {
        try {
            const mentalStatusExams = await database_1.prisma.mentalStatusExam.findMany({
                where: { patientId },
                orderBy: { examDate: 'desc' },
                take: limit,
                include: {
                    patient: {
                        select: {
                            id: true,
                            patientId: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    examiner: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            role: true,
                        },
                    },
                },
            });
            return {
                success: true,
                data: mentalStatusExams,
            };
        }
        catch (error) {
            console.error('Error fetching patient mental status exams:', error);
            return {
                success: false,
                error: 'Failed to fetch patient mental status exams',
                data: [],
            };
        }
    }
    static async updateMentalStatusExam(id, data, updatedBy, auditContext) {
        try {
            const existingExam = await database_1.prisma.mentalStatusExam.findUnique({
                where: { id },
            });
            if (!existingExam) {
                return {
                    success: false,
                    error: 'Mental status exam not found',
                };
            }
            const updateData = {};
            Object.keys(data).forEach(key => {
                if (data[key] !== undefined) {
                    if (key === 'examDate') {
                        updateData[key] = new Date(data[key]);
                    }
                    else {
                        updateData[key] = data[key];
                    }
                }
            });
            const updatedExam = await database_1.prisma.mentalStatusExam.update({
                where: { id },
                data: updateData,
                include: {
                    patient: {
                        select: {
                            id: true,
                            patientId: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    examiner: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            role: true,
                        },
                    },
                },
            });
            return {
                success: true,
                data: updatedExam,
                message: 'Mental status exam updated successfully',
            };
        }
        catch (error) {
            console.error('Error updating mental status exam:', error);
            return {
                success: false,
                error: 'Failed to update mental status exam',
            };
        }
    }
    static async deleteMentalStatusExam(id, deletedBy, auditContext) {
        try {
            const existingExam = await database_1.prisma.mentalStatusExam.findUnique({
                where: { id },
            });
            if (!existingExam) {
                return {
                    success: false,
                    error: 'Mental status exam not found',
                };
            }
            await database_1.prisma.mentalStatusExam.delete({
                where: { id },
            });
            return {
                success: true,
                message: 'Mental status exam deleted successfully',
            };
        }
        catch (error) {
            console.error('Error deleting mental status exam:', error);
            return {
                success: false,
                error: 'Failed to delete mental status exam',
            };
        }
    }
}
exports.MentalStatusExamService = MentalStatusExamService;
//# sourceMappingURL=mentalStatusExamService.js.map