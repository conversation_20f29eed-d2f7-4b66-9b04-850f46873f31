"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppointmentController = void 0;
const zod_1 = require("zod");
const appointmentService_1 = require("@/services/appointmentService");
const validation_1 = require("../utils/validation");
const appointmentQuerySchema = zod_1.z.object({
    page: zod_1.z.string().optional(),
    limit: zod_1.z.string().optional(),
    patientId: zod_1.z.string().uuid().optional(),
    providerId: zod_1.z.string().uuid().optional(),
    status: zod_1.z.string().optional(),
    type: zod_1.z.string().optional(),
    dateFrom: zod_1.z.string().optional(),
    dateTo: zod_1.z.string().optional(),
    sortBy: zod_1.z.string().optional(),
    sortOrder: zod_1.z.enum(['asc', 'desc']).optional(),
});
const cancelAppointmentSchema = zod_1.z.object({
    reason: zod_1.z.string().min(1, 'Cancellation reason is required').max(500),
});
const availabilityQuerySchema = zod_1.z.object({
    date: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format'),
});
class AppointmentController {
    static async createAppointment(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            const validatedData = validation_1.createAppointmentSchema.parse(req.body);
            const result = await appointmentService_1.AppointmentService.createAppointment(validatedData, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') || 'unknown' });
            res.status(201).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getAppointments(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            const validatedQuery = appointmentQuerySchema.parse(req.query);
            const result = await appointmentService_1.AppointmentService.getAppointments(validatedQuery, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getAppointmentById(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            const { id } = req.params;
            const result = await appointmentService_1.AppointmentService.getAppointmentById(id, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async updateAppointment(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            const validatedData = validation_1.updateAppointmentSchema.parse(req.body);
            const result = await appointmentService_1.AppointmentService.updateAppointment(req.params.id, validatedData, req.user.id, req.user.role, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async cancelAppointment(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            const { id } = req.params;
            const { reason } = cancelAppointmentSchema.parse(req.body);
            const auditData = {
                ipAddress: req.ip,
                userAgent: req.get('User-Agent'),
            };
            const result = await appointmentService_1.AppointmentService.cancelAppointment(id, reason, req.user.id, req.user.role, auditData);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getProviderAvailability(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            const { providerId } = req.params;
            const { date } = availabilityQuerySchema.parse(req.query);
            const result = await appointmentService_1.AppointmentService.getProviderAvailability(providerId, date, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getAppointmentStats(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            res.status(200).json({
                success: true,
                data: {
                    totalAppointments: 0,
                    todayAppointments: 0,
                    upcomingAppointments: 0,
                    completedThisWeek: 0,
                    statusDistribution: {
                        SCHEDULED: 0,
                        CONFIRMED: 0,
                        COMPLETED: 0,
                        CANCELLED: 0,
                        NO_SHOW: 0,
                    },
                    typeDistribution: {
                        CONSULTATION: 0,
                        FOLLOW_UP: 0,
                        THERAPY: 0,
                        ASSESSMENT: 0,
                        OTHER: 0,
                    },
                },
                message: 'Appointment statistics retrieved successfully',
            });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.AppointmentController = AppointmentController;
//# sourceMappingURL=appointmentController.js.map