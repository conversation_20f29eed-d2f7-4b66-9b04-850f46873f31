"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MedicationHistoryService = void 0;
const database_1 = require("../utils/database");
class MedicationHistoryService {
    static async createMedicationHistory(data, createdBy, auditContext) {
        try {
            const patient = await database_1.prisma.patient.findUnique({
                where: { id: data.patientId },
            });
            if (!patient) {
                return {
                    success: false,
                    error: 'Patient not found',
                };
            }
            const startDate = new Date(data.startDate);
            const endDate = data.endDate ? new Date(data.endDate) : null;
            const medicationHistory = await database_1.prisma.medicationHistory.create({
                data: {
                    patientId: data.patientId,
                    medicationName: data.medicationName.trim(),
                    genericName: data.genericName?.trim() || null,
                    brandName: data.brandName?.trim() || null,
                    strength: data.strength.trim(),
                    dosage: data.dosage.trim(),
                    frequency: data.frequency.trim(),
                    route: data.route.trim(),
                    indication: data.indication.trim(),
                    startDate,
                    endDate,
                    duration: data.duration?.trim() || null,
                    prescribedBy: data.prescribedBy.trim(),
                    prescriberId: data.prescriberId || null,
                    pharmacy: data.pharmacy?.trim() || null,
                    sideEffects: data.sideEffects || null,
                    effectiveness: data.effectiveness || null,
                    adherence: data.adherence || null,
                    adherenceNotes: data.adherenceNotes?.trim() || null,
                    discontinuedReason: data.discontinuedReason?.trim() || null,
                    allergicReaction: data.allergicReaction || false,
                    interactions: data.interactions || null,
                    monitoring: data.monitoring?.trim() || null,
                    prn: data.prn || false,
                    notes: data.notes?.trim() || null,
                    isActive: data.isActive !== false,
                },
                include: {
                    patient: {
                        select: {
                            id: true,
                            patientId: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    prescriber: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            role: true,
                        },
                    },
                },
            });
            return {
                success: true,
                data: medicationHistory,
                message: 'Medication history created successfully',
            };
        }
        catch (error) {
            console.error('Error creating medication history:', error);
            return {
                success: false,
                error: 'Failed to create medication history',
            };
        }
    }
    static async getAllMedicationHistory(filters, page = 1, limit = 10) {
        try {
            const skip = (page - 1) * limit;
            const where = {};
            if (filters.patientId) {
                where.patientId = filters.patientId;
            }
            if (filters.medicationName) {
                where.medicationName = {
                    contains: filters.medicationName,
                    mode: 'insensitive',
                };
            }
            if (filters.isActive !== undefined) {
                where.isActive = filters.isActive;
            }
            const [medicationHistory, total] = await Promise.all([
                database_1.prisma.medicationHistory.findMany({
                    where,
                    include: {
                        patient: {
                            select: {
                                id: true,
                                patientId: true,
                                firstName: true,
                                lastName: true,
                            },
                        },
                        prescriber: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                role: true,
                            },
                        },
                    },
                    orderBy: { startDate: 'desc' },
                    skip,
                    take: limit,
                }),
                database_1.prisma.medicationHistory.count({ where }),
            ]);
            return {
                success: true,
                data: medicationHistory,
                pagination: {
                    page,
                    limit,
                    total,
                    pages: Math.ceil(total / limit),
                },
            };
        }
        catch (error) {
            console.error('Error fetching medication history:', error);
            return {
                success: false,
                error: 'Failed to fetch medication history',
                data: [],
                pagination: { page, limit, total: 0, pages: 0 },
            };
        }
    }
    static async getMedicationHistoryById(id) {
        try {
            const medicationHistory = await database_1.prisma.medicationHistory.findUnique({
                where: { id },
                include: {
                    patient: {
                        select: {
                            id: true,
                            patientId: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    prescriber: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            role: true,
                        },
                    },
                },
            });
            if (!medicationHistory) {
                return {
                    success: false,
                    error: 'Medication history not found',
                };
            }
            return {
                success: true,
                data: medicationHistory,
            };
        }
        catch (error) {
            console.error('Error fetching medication history:', error);
            return {
                success: false,
                error: 'Failed to fetch medication history',
            };
        }
    }
    static async getMedicationHistoryByPatient(patientId, isActive, limit = 20) {
        try {
            const where = { patientId };
            if (isActive !== undefined) {
                where.isActive = isActive;
            }
            const medicationHistory = await database_1.prisma.medicationHistory.findMany({
                where,
                orderBy: { startDate: 'desc' },
                take: limit,
                include: {
                    patient: {
                        select: {
                            id: true,
                            patientId: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    prescriber: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            role: true,
                        },
                    },
                },
            });
            return {
                success: true,
                data: medicationHistory,
            };
        }
        catch (error) {
            console.error('Error fetching patient medication history:', error);
            return {
                success: false,
                error: 'Failed to fetch patient medication history',
                data: [],
            };
        }
    }
    static async updateMedicationHistory(id, data, updatedBy, auditContext) {
        try {
            const existingMedication = await database_1.prisma.medicationHistory.findUnique({
                where: { id },
            });
            if (!existingMedication) {
                return {
                    success: false,
                    error: 'Medication history not found',
                };
            }
            const updateData = {};
            Object.keys(data).forEach(key => {
                const value = data[key];
                if (value !== undefined) {
                    if (key === 'startDate' || key === 'endDate') {
                        updateData[key] = value ? new Date(value) : null;
                    }
                    else if (typeof value === 'string') {
                        updateData[key] = value.trim();
                    }
                    else {
                        updateData[key] = value;
                    }
                }
            });
            const updatedMedication = await database_1.prisma.medicationHistory.update({
                where: { id },
                data: updateData,
                include: {
                    patient: {
                        select: {
                            id: true,
                            patientId: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    prescriber: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            role: true,
                        },
                    },
                },
            });
            return {
                success: true,
                data: updatedMedication,
                message: 'Medication history updated successfully',
            };
        }
        catch (error) {
            console.error('Error updating medication history:', error);
            return {
                success: false,
                error: 'Failed to update medication history',
            };
        }
    }
    static async deleteMedicationHistory(id, deletedBy, auditContext) {
        try {
            const existingMedication = await database_1.prisma.medicationHistory.findUnique({
                where: { id },
            });
            if (!existingMedication) {
                return {
                    success: false,
                    error: 'Medication history not found',
                };
            }
            await database_1.prisma.medicationHistory.delete({
                where: { id },
            });
            return {
                success: true,
                message: 'Medication history deleted successfully',
            };
        }
        catch (error) {
            console.error('Error deleting medication history:', error);
            return {
                success: false,
                error: 'Failed to delete medication history',
            };
        }
    }
}
exports.MedicationHistoryService = MedicationHistoryService;
//# sourceMappingURL=medicationHistoryService.js.map