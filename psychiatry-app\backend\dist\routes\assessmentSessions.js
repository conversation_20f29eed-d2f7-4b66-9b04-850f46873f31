"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const assessmentController_1 = require("@/controllers/assessmentController");
const auth_1 = require("@/middleware/auth");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/:id', assessmentController_1.AssessmentController.getAssessmentSessionById);
exports.default = router;
//# sourceMappingURL=assessmentSessions.js.map