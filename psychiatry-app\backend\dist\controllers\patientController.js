"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientController = void 0;
const zod_1 = require("zod");
const patientService_1 = require("@/services/patientService");
const validation_1 = require("../utils/validation");
const patientQuerySchema = zod_1.z.object({
    page: zod_1.z.string().optional(),
    limit: zod_1.z.string().optional(),
    search: zod_1.z.string().optional(),
    gender: zod_1.z.enum(['MALE', 'FEMALE', 'NON_BINARY', 'PREFER_NOT_TO_SAY', 'OTHER']).optional(),
    isActive: zod_1.z.string().optional(),
    sortBy: zod_1.z.string().optional(),
    sortOrder: zod_1.z.enum(['asc', 'desc']).optional(),
});
class PatientController {
    static async getPatientStats(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const result = await patientService_1.PatientService.getPatientStats(req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async createPatient(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const validatedData = validation_1.createPatientSchema.parse(req.body);
            const result = await patientService_1.PatientService.createPatient(validatedData, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            res.status(201).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getPatients(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const query = patientQuerySchema.parse(req.query);
            const result = await patientService_1.PatientService.getPatients(query, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getPatientById(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const result = await patientService_1.PatientService.getPatientById(req.params.id, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async updatePatient(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const validatedData = validation_1.updatePatientSchema.parse(req.body);
            const result = await patientService_1.PatientService.updatePatient(req.params.id, validatedData, req.user.id, req.user.role, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async deletePatient(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const result = await patientService_1.PatientService.deletePatient(req.params.id, req.user.id, req.user.role, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async searchPatients(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { term } = req.query;
            if (!term || typeof term !== 'string') {
                res.status(400).json({ success: false, error: 'Search term is required' });
                return;
            }
            const result = await patientService_1.PatientService.searchPatients(term, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
}
exports.PatientController = PatientController;
//# sourceMappingURL=patientController.js.map