"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const labResultController_1 = require("@/controllers/labResultController");
const auth_1 = require("@/middleware/auth");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/', labResultController_1.LabResultController.getLabResults);
router.post('/', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN']), labResultController_1.LabResultController.createLabResult);
router.get('/:id', labResultController_1.LabResultController.getLabResultById);
router.delete('/:id', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN']), labResultController_1.LabResultController.deleteLabResult);
exports.default = router;
//# sourceMappingURL=labResults.js.map