"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authController_1 = require("@/controllers/authController");
const auth_1 = require("@/middleware/auth");
const security_1 = require("@/middleware/security");
const router = (0, express_1.Router)();
router.post('/register', security_1.authRateLimit, authController_1.AuthController.register);
router.post('/login', security_1.authRateLimit, authController_1.AuthController.login);
router.post('/refresh', authController_1.AuthController.refreshToken);
router.post('/logout', authController_1.AuthController.logout);
router.get('/me', auth_1.authenticate, authController_1.AuthController.getCurrentUser);
router.put('/change-password', auth_1.authenticate, security_1.passwordResetRateLimit, authController_1.AuthController.changePassword);
exports.default = router;
//# sourceMappingURL=auth.js.map