"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MentalStatusExamController = void 0;
const mentalStatusExamService_1 = require("../services/mentalStatusExamService");
const validation_1 = require("../utils/validation");
class MentalStatusExamController {
    static async createMentalStatusExam(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const validatedData = validation_1.createMentalStatusExamSchema.parse(req.body);
            const result = await mentalStatusExamService_1.MentalStatusExamService.createMentalStatusExam(validatedData, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            res.status(201).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getAllMentalStatusExams(req, res, next) {
        try {
            const { patientId, examinerId, page = 1, limit = 10 } = req.query;
            const filters = {
                patientId: patientId,
                examinerId: examinerId,
            };
            const result = await mentalStatusExamService_1.MentalStatusExamService.getAllMentalStatusExams(filters, parseInt(page), parseInt(limit));
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getMentalStatusExamById(req, res, next) {
        try {
            const { id } = req.params;
            const result = await mentalStatusExamService_1.MentalStatusExamService.getMentalStatusExamById(id);
            if (!result.success) {
                res.status(404).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getMentalStatusExamsByPatient(req, res, next) {
        try {
            const { patientId } = req.params;
            const { limit = 10 } = req.query;
            const result = await mentalStatusExamService_1.MentalStatusExamService.getMentalStatusExamsByPatient(patientId, parseInt(limit));
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async updateMentalStatusExam(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { id } = req.params;
            const validatedData = validation_1.updateMentalStatusExamSchema.parse(req.body);
            const result = await mentalStatusExamService_1.MentalStatusExamService.updateMentalStatusExam(id, validatedData, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            if (!result.success) {
                res.status(404).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async deleteMentalStatusExam(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { id } = req.params;
            const result = await mentalStatusExamService_1.MentalStatusExamService.deleteMentalStatusExam(id, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            if (!result.success) {
                res.status(404).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
}
exports.MentalStatusExamController = MentalStatusExamController;
//# sourceMappingURL=mentalStatusExamController.js.map