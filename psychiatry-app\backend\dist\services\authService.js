"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const client_1 = require("@prisma/client");
const uuid_1 = require("uuid");
const password_1 = require("@/utils/password");
const jwt_1 = require("@/utils/jwt");
const errors_1 = require("@/utils/errors");
const prisma = new client_1.PrismaClient();
const MAX_LOGIN_ATTEMPTS = parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5', 10);
const LOCKOUT_TIME_MS = parseInt(process.env.LOCKOUT_TIME_MS || '900000', 10);
class AuthService {
    static async register(data) {
        const passwordValidation = (0, password_1.validatePasswordStrength)(data.password);
        if (!passwordValidation.isValid) {
            throw new errors_1.ValidationError(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
        }
        const existingUser = await prisma.user.findFirst({
            where: {
                OR: [
                    { username: data.username },
                    { email: data.email },
                ],
            },
        });
        if (existingUser) {
            if (existingUser.username === data.username) {
                throw new errors_1.ConflictError('Username already exists');
            }
            if (existingUser.email === data.email) {
                throw new errors_1.ConflictError('Email already exists');
            }
        }
        const hashedPassword = await (0, password_1.hashPassword)(data.password);
        const user = await prisma.user.create({
            data: {
                username: data.username,
                email: data.email,
                password: hashedPassword,
                firstName: data.firstName,
                lastName: data.lastName,
                role: data.role || 'CLINICIAN',
            },
            select: {
                id: true,
                username: true,
                email: true,
                role: true,
                firstName: true,
                lastName: true,
                createdAt: true,
            },
        });
        await this.createAuditLog({
            userId: user.id,
            action: 'CREATE',
            entityType: 'USER',
            entityId: user.id,
            newValues: {
                username: user.username,
                email: user.email,
                role: user.role,
            },
        });
        return {
            success: true,
            data: {
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    role: user.role,
                    firstName: user.firstName,
                    lastName: user.lastName,
                },
            },
            message: 'User registered successfully',
        };
    }
    static async login(credentials, ipAddress, userAgent) {
        const { username, password } = credentials;
        const user = await prisma.user.findFirst({
            where: {
                OR: [
                    { username },
                    { email: username },
                ],
            },
        });
        if (!user) {
            throw new errors_1.AuthenticationError('Invalid credentials');
        }
        if (user.lockedUntil && user.lockedUntil > new Date()) {
            const lockoutMinutes = Math.ceil((user.lockedUntil.getTime() - Date.now()) / 60000);
            throw new errors_1.AuthenticationError(`Account is locked. Try again in ${lockoutMinutes} minutes.`);
        }
        if (!user.isActive) {
            throw new errors_1.AuthenticationError('Account is deactivated');
        }
        const isPasswordValid = await (0, password_1.comparePassword)(password, user.password);
        if (!isPasswordValid) {
            const newAttempts = user.loginAttempts + 1;
            const shouldLock = newAttempts >= MAX_LOGIN_ATTEMPTS;
            await prisma.user.update({
                where: { id: user.id },
                data: {
                    loginAttempts: newAttempts,
                    lockedUntil: shouldLock ? new Date(Date.now() + LOCKOUT_TIME_MS) : null,
                },
            });
            if (shouldLock) {
                throw new errors_1.AuthenticationError(`Account locked due to too many failed login attempts. Try again in ${Math.ceil(LOCKOUT_TIME_MS / 60000)} minutes.`);
            }
            throw new errors_1.AuthenticationError('Invalid credentials');
        }
        await prisma.user.update({
            where: { id: user.id },
            data: {
                loginAttempts: 0,
                lockedUntil: null,
                lastLogin: new Date(),
            },
        });
        const tokenId = (0, uuid_1.v4)();
        const accessToken = (0, jwt_1.generateAccessToken)({
            userId: user.id,
            username: user.username,
            role: user.role,
        });
        const refreshToken = (0, jwt_1.generateRefreshToken)({
            userId: user.id,
            tokenId,
        });
        await prisma.refreshToken.create({
            data: {
                id: tokenId,
                token: refreshToken,
                userId: user.id,
                expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            },
        });
        await this.createAuditLog({
            userId: user.id,
            action: 'VIEW',
            entityType: 'USER',
            entityId: user.id,
            ipAddress,
            userAgent,
        });
        return {
            success: true,
            data: {
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    role: user.role,
                    firstName: user.firstName,
                    lastName: user.lastName,
                },
                accessToken,
                refreshToken,
            },
            message: 'Login successful',
        };
    }
    static async refreshToken(refreshToken) {
        try {
            const payload = (0, jwt_1.verifyRefreshToken)(refreshToken);
            const storedToken = await prisma.refreshToken.findUnique({
                where: { id: payload.tokenId },
                include: { user: true },
            });
            if (!storedToken || storedToken.isRevoked || storedToken.expiresAt < new Date()) {
                throw new errors_1.AuthenticationError('Invalid or expired refresh token');
            }
            if (!storedToken.user.isActive) {
                throw new errors_1.AuthenticationError('Account is deactivated');
            }
            const newTokenId = (0, uuid_1.v4)();
            const newAccessToken = (0, jwt_1.generateAccessToken)({
                userId: storedToken.user.id,
                username: storedToken.user.username,
                role: storedToken.user.role,
            });
            const newRefreshToken = (0, jwt_1.generateRefreshToken)({
                userId: storedToken.user.id,
                tokenId: newTokenId,
            });
            await prisma.$transaction([
                prisma.refreshToken.update({
                    where: { id: payload.tokenId },
                    data: { isRevoked: true },
                }),
                prisma.refreshToken.create({
                    data: {
                        id: newTokenId,
                        token: newRefreshToken,
                        userId: storedToken.user.id,
                        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
                    },
                }),
            ]);
            return {
                success: true,
                data: {
                    accessToken: newAccessToken,
                    refreshToken: newRefreshToken,
                },
                message: 'Token refreshed successfully',
            };
        }
        catch (error) {
            throw new errors_1.AuthenticationError('Invalid refresh token');
        }
    }
    static async logout(refreshToken) {
        try {
            const payload = (0, jwt_1.verifyRefreshToken)(refreshToken);
            await prisma.refreshToken.update({
                where: { id: payload.tokenId },
                data: { isRevoked: true },
            });
            return {
                success: true,
                data: null,
                message: 'Logout successful',
            };
        }
        catch {
            return {
                success: true,
                data: null,
                message: 'Logout successful',
            };
        }
    }
    static async getCurrentUser(userId) {
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: {
                id: true,
                username: true,
                email: true,
                role: true,
                firstName: true,
                lastName: true,
                lastLogin: true,
                createdAt: true,
            },
        });
        if (!user) {
            throw new errors_1.NotFoundError('User not found');
        }
        return {
            success: true,
            data: {
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    role: user.role,
                    firstName: user.firstName,
                    lastName: user.lastName,
                },
            },
        };
    }
    static async changePassword(userId, currentPassword, newPassword) {
        const user = await prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new errors_1.NotFoundError('User not found');
        }
        const isCurrentPasswordValid = await (0, password_1.comparePassword)(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            throw new errors_1.AuthenticationError('Current password is incorrect');
        }
        const passwordValidation = (0, password_1.validatePasswordStrength)(newPassword);
        if (!passwordValidation.isValid) {
            throw new errors_1.ValidationError(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
        }
        const hashedNewPassword = await (0, password_1.hashPassword)(newPassword);
        await prisma.user.update({
            where: { id: userId },
            data: { password: hashedNewPassword },
        });
        await prisma.refreshToken.updateMany({
            where: { userId },
            data: { isRevoked: true },
        });
        await this.createAuditLog({
            userId,
            action: 'UPDATE',
            entityType: 'USER',
            entityId: userId,
            newValues: { passwordChanged: true },
        });
        return {
            success: true,
            data: null,
            message: 'Password changed successfully',
        };
    }
    static async createAuditLog(data) {
        try {
            await prisma.auditLog.create({
                data: {
                    userId: data.userId,
                    action: data.action,
                    entityType: data.entityType,
                    entityId: data.entityId,
                    oldValues: data.oldValues ? JSON.stringify(data.oldValues) : null,
                    newValues: data.newValues ? JSON.stringify(data.newValues) : null,
                    ipAddress: data.ipAddress,
                    userAgent: data.userAgent,
                    patientId: data.patientId,
                    labResultId: data.labResultId,
                    appointmentId: data.appointmentId,
                },
            });
        }
        catch (error) {
            console.error('Failed to create audit log:', error);
        }
    }
}
exports.AuthService = AuthService;
//# sourceMappingURL=authService.js.map