"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PsychTestController = void 0;
const psychTestService_1 = require("../services/psychTestService");
const validation_1 = require("../utils/validation");
class PsychTestController {
    static async createPsychTest(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const validatedData = validation_1.createPsychTestSchema.parse(req.body);
            const result = await psychTestService_1.PsychTestService.createPsychTest(validatedData, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            res.status(201).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getAllPsychTests(req, res, next) {
        try {
            const { patientId, testName, testCategory, page = 1, limit = 10 } = req.query;
            const filters = {
                patientId: patientId,
                testName: testName,
                testCategory: testCategory,
            };
            const result = await psychTestService_1.PsychTestService.getAllPsychTests(filters, parseInt(page), parseInt(limit));
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getPsychTestById(req, res, next) {
        try {
            const { id } = req.params;
            const result = await psychTestService_1.PsychTestService.getPsychTestById(id);
            if (!result.success) {
                res.status(404).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getPsychTestsByPatient(req, res, next) {
        try {
            const { patientId } = req.params;
            const { testCategory, limit = 10 } = req.query;
            const result = await psychTestService_1.PsychTestService.getPsychTestsByPatient(patientId, testCategory, parseInt(limit));
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async updatePsychTest(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { id } = req.params;
            const validatedData = validation_1.updatePsychTestSchema.parse(req.body);
            const result = await psychTestService_1.PsychTestService.updatePsychTest(id, validatedData, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            if (!result.success) {
                res.status(404).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async deletePsychTest(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { id } = req.params;
            const result = await psychTestService_1.PsychTestService.deletePsychTest(id, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            if (!result.success) {
                res.status(404).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
}
exports.PsychTestController = PsychTestController;
//# sourceMappingURL=psychTestController.js.map