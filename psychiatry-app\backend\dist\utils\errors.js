"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatErrorResponse = exports.ServerError = exports.RateLimitError = exports.ConflictError = exports.NotFoundError = exports.AuthorizationError = exports.AuthenticationError = exports.ValidationError = exports.AppError = void 0;
class AppError extends Error {
    constructor(message, statusCode, isOperational = true) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
class ValidationError extends AppError {
    constructor(message, field, value) {
        super(message, 400);
        this.field = field;
        this.value = value;
    }
}
exports.ValidationError = ValidationError;
class AuthenticationError extends AppError {
    constructor(message = 'Authentication failed') {
        super(message, 401);
    }
}
exports.AuthenticationError = AuthenticationError;
class AuthorizationError extends AppError {
    constructor(message = 'Access denied') {
        super(message, 403);
    }
}
exports.AuthorizationError = AuthorizationError;
class NotFoundError extends AppError {
    constructor(message = 'Resource not found') {
        super(message, 404);
    }
}
exports.NotFoundError = NotFoundError;
class ConflictError extends AppError {
    constructor(message = 'Resource conflict') {
        super(message, 409);
    }
}
exports.ConflictError = ConflictError;
class RateLimitError extends AppError {
    constructor(message = 'Too many requests') {
        super(message, 429);
    }
}
exports.RateLimitError = RateLimitError;
class ServerError extends AppError {
    constructor(message = 'Internal server error') {
        super(message, 500, false);
    }
}
exports.ServerError = ServerError;
const formatErrorResponse = (error) => {
    if (error instanceof AppError) {
        return {
            success: false,
            error: error.message,
            statusCode: error.statusCode,
            ...(error instanceof ValidationError && {
                field: error.field,
                value: error.value,
            }),
        };
    }
    if (error.name === 'PrismaClientKnownRequestError') {
        const prismaError = error;
        switch (prismaError.code) {
            case 'P2002':
                return {
                    success: false,
                    error: 'A record with this information already exists',
                    statusCode: 409,
                };
            case 'P2025':
                return {
                    success: false,
                    error: 'Record not found',
                    statusCode: 404,
                };
            default:
                return {
                    success: false,
                    error: 'Database operation failed',
                    statusCode: 500,
                };
        }
    }
    if (error.name === 'ZodError' || error.name === 'ValidationError') {
        return {
            success: false,
            error: 'Validation failed',
            statusCode: 400,
            details: error.message,
        };
    }
    return {
        success: false,
        error: process.env.NODE_ENV === 'production'
            ? 'Internal server error'
            : error.message,
        statusCode: 500,
    };
};
exports.formatErrorResponse = formatErrorResponse;
//# sourceMappingURL=errors.js.map