"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updatePatientSchema = exports.createPatientSchema = exports.createNotificationSchema = exports.updateMedicationHistorySchema = exports.createMedicationHistorySchema = exports.updateMentalStatusExamSchema = exports.createMentalStatusExamSchema = exports.updatePsychTestSchema = exports.createPsychTestSchema = exports.updateLabResultSchema = exports.createLabResultSchema = exports.loginSchema = exports.registerSchema = exports.updateAppointmentSchema = exports.createAppointmentSchema = void 0;
const zod_1 = require("zod");
exports.createAppointmentSchema = zod_1.z.object({
    patientId: zod_1.z.string().uuid('Invalid patient ID'),
    providerId: zod_1.z.string().uuid('Invalid provider ID').optional().or(zod_1.z.literal('')),
    date: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format'),
    duration: zod_1.z.number().min(15, 'Duration must be at least 15 minutes').max(480, 'Duration cannot exceed 8 hours'),
    type: zod_1.z.enum([
        'INITIAL_CONSULTATION',
        'FOLLOW_UP',
        'THERAPY_SESSION',
        'MEDICATION_REVIEW',
        'CRISIS_INTERVENTION',
        'GROUP_THERAPY',
        'FAMILY_THERAPY',
        'PSYCHOLOGICAL_TESTING',
        'OTHER',
    ]),
    status: zod_1.z.enum(['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW', 'RESCHEDULED']).optional(),
    title: zod_1.z.string().max(200).optional(),
    description: zod_1.z.string().max(1000).optional(),
    location: zod_1.z.string().max(200).optional(),
    isVirtual: zod_1.z.boolean().optional(),
    virtualMeetingUrl: zod_1.z.string().url().optional(),
    notes: zod_1.z.string().max(2000).optional(),
    recurringAppointmentId: zod_1.z.string().uuid().optional(),
});
exports.updateAppointmentSchema = exports.createAppointmentSchema.partial();
exports.registerSchema = zod_1.z.object({
    username: zod_1.z.string()
        .min(3, 'Username must be at least 3 characters')
        .max(50, 'Username must be less than 50 characters')
        .regex(/^[a-zA-Z0-9._-]+$/, 'Username can only contain letters, numbers, dots, underscores, and hyphens'),
    email: zod_1.z.string()
        .email('Invalid email format')
        .max(255, 'Email must be less than 255 characters'),
    password: zod_1.z.string()
        .min(8, 'Password must be at least 8 characters'),
    firstName: zod_1.z.string()
        .min(1, 'First name is required')
        .max(100, 'First name must be less than 100 characters'),
    lastName: zod_1.z.string()
        .min(1, 'Last name is required')
        .max(100, 'Last name must be less than 100 characters'),
    role: zod_1.z.enum(['ADMIN', 'CLINICIAN', 'STAFF']).optional(),
});
exports.loginSchema = zod_1.z.object({
    username: zod_1.z.string()
        .min(1, 'Username or email is required'),
    password: zod_1.z.string()
        .min(1, 'Password is required'),
});
exports.createLabResultSchema = zod_1.z.object({
    patientId: zod_1.z.string().uuid(),
    testType: zod_1.z.enum([
        'CBC',
        'METABOLIC_PANEL',
        'LIPID_PANEL',
        'THYROID',
        'LIVER_FUNCTION',
        'KIDNEY_FUNCTION',
        'VITAMIN_LEVELS',
        'DRUG_SCREEN',
        'CARDIAC_MARKERS',
        'INFLAMMATORY',
        'COAGULATION',
        'URINALYSIS',
        'HEMOGLOBIN_A1C',
        'OTHER',
    ]),
    testDate: zod_1.z.string().datetime(),
    orderedBy: zod_1.z.string(),
    labName: zod_1.z.string().optional(),
    results: zod_1.z.record(zod_1.z.any()),
    normalRanges: zod_1.z.record(zod_1.z.any()).optional(),
    flags: zod_1.z.record(zod_1.z.any()).optional(),
    notes: zod_1.z.string().optional(),
    status: zod_1.z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'AMENDED']).optional(),
});
exports.updateLabResultSchema = exports.createLabResultSchema.partial();
exports.createPsychTestSchema = zod_1.z.object({
    patientId: zod_1.z.string().uuid('Invalid patient ID'),
    testName: zod_1.z.string().min(1, 'Test name is required').max(100, 'Test name must be less than 100 characters'),
    testCategory: zod_1.z.enum(['depression', 'anxiety', 'ocd', 'psychosis', 'cognitive', 'personality', 'trauma', 'substance', 'other']),
    version: zod_1.z.string().max(50).optional(),
    administeredBy: zod_1.z.string().min(1, 'Administrator name is required').max(100),
    administeredDate: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format'),
    completionTime: zod_1.z.number().min(1).max(600).optional(),
    location: zod_1.z.enum(['office', 'hospital', 'telehealth', 'home', 'other']).optional(),
    rawScore: zod_1.z.number().optional(),
    totalScore: zod_1.z.number().optional(),
    subscaleScores: zod_1.z.string().optional(),
    scaledScore: zod_1.z.number().optional(),
    percentile: zod_1.z.number().min(0).max(100).optional(),
    tScore: zod_1.z.number().optional(),
    zScore: zod_1.z.number().optional(),
    severity: zod_1.z.enum(['minimal', 'mild', 'moderate', 'severe', 'extreme']).optional(),
    clinicalRange: zod_1.z.enum(['normal', 'borderline', 'clinical']).optional(),
    interpretation: zod_1.z.string().max(2000).optional(),
    recommendations: zod_1.z.string().max(2000).optional(),
    responses: zod_1.z.string().min(1, 'Test responses are required'),
    validity: zod_1.z.enum(['valid', 'questionable', 'invalid']).optional(),
    validityIndices: zod_1.z.string().optional(),
    notes: zod_1.z.string().max(1000).optional(),
    followUpDate: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format').optional(),
    followUpRequired: zod_1.z.boolean().optional(),
    batteryId: zod_1.z.string().max(100).optional(),
    sessionNumber: zod_1.z.number().min(1).optional(),
    baselineTest: zod_1.z.boolean().optional(),
});
exports.updatePsychTestSchema = exports.createPsychTestSchema.partial();
exports.createMentalStatusExamSchema = zod_1.z.object({
    patientId: zod_1.z.string().uuid('Invalid patient ID'),
    examDate: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format'),
    examinerId: zod_1.z.string().uuid('Invalid examiner ID'),
    appearance_grooming: zod_1.z.enum(['well-groomed', 'disheveled', 'inappropriate', 'poor']).optional(),
    appearance_dress: zod_1.z.enum(['appropriate', 'bizarre', 'seductive', 'inappropriate']).optional(),
    appearance_hygiene: zod_1.z.enum(['good', 'poor', 'neglected']).optional(),
    behavior_eye_contact: zod_1.z.enum(['appropriate', 'poor', 'intense', 'avoidant']).optional(),
    behavior_motor: zod_1.z.enum(['normal', 'agitated', 'retarded', 'restless', 'hyperactive']).optional(),
    behavior_cooperation: zod_1.z.enum(['cooperative', 'guarded', 'hostile', 'withdrawn']).optional(),
    speech_rate: zod_1.z.enum(['normal', 'pressured', 'slow', 'rapid']).optional(),
    speech_volume: zod_1.z.enum(['normal', 'loud', 'soft', 'whispered']).optional(),
    speech_tone: zod_1.z.enum(['normal', 'monotone', 'dramatic', 'anxious']).optional(),
    speech_fluency: zod_1.z.enum(['fluent', 'dysfluent', 'stuttering']).optional(),
    mood_reported: zod_1.z.string().max(200).optional(),
    mood_observed: zod_1.z.string().max(200).optional(),
    affect_type: zod_1.z.enum(['euthymic', 'depressed', 'anxious', 'irritable', 'euphoric']).optional(),
    affect_range: zod_1.z.enum(['full', 'restricted', 'blunted', 'flat']).optional(),
    affect_appropriateness: zod_1.z.enum(['appropriate', 'inappropriate', 'incongruent']).optional(),
    thought_process: zod_1.z.enum(['linear', 'tangential', 'circumstantial', 'loose', 'flight_of_ideas']).optional(),
    thought_organization: zod_1.z.enum(['organized', 'disorganized', 'coherent']).optional(),
    thought_flow: zod_1.z.enum(['normal', 'rapid', 'slow', 'blocked']).optional(),
    thought_content: zod_1.z.string().optional(),
    delusions: zod_1.z.boolean().optional(),
    delusion_type: zod_1.z.enum(['persecutory', 'grandiose', 'somatic', 'religious', 'nihilistic']).optional(),
    obsessions: zod_1.z.boolean().optional(),
    compulsions: zod_1.z.boolean().optional(),
    phobias: zod_1.z.boolean().optional(),
    hallucinations: zod_1.z.boolean().optional(),
    hallucination_type: zod_1.z.enum(['auditory', 'visual', 'tactile', 'olfactory', 'gustatory']).optional(),
    illusions: zod_1.z.boolean().optional(),
    depersonalization: zod_1.z.boolean().optional(),
    derealization: zod_1.z.boolean().optional(),
    orientation_person: zod_1.z.boolean().optional(),
    orientation_place: zod_1.z.boolean().optional(),
    orientation_time: zod_1.z.boolean().optional(),
    orientation_situation: zod_1.z.boolean().optional(),
    attention_span: zod_1.z.enum(['good', 'fair', 'poor', 'distractible']).optional(),
    concentration: zod_1.z.enum(['good', 'fair', 'poor', 'impaired']).optional(),
    memory_immediate: zod_1.z.enum(['intact', 'impaired', 'unable_to_assess']).optional(),
    memory_recent: zod_1.z.enum(['intact', 'impaired', 'unable_to_assess']).optional(),
    memory_remote: zod_1.z.enum(['intact', 'impaired', 'unable_to_assess']).optional(),
    abstract_thinking: zod_1.z.enum(['intact', 'concrete', 'impaired']).optional(),
    insight_level: zod_1.z.enum(['good', 'fair', 'poor', 'absent']).optional(),
    insight_description: zod_1.z.string().max(500).optional(),
    judgment_level: zod_1.z.enum(['good', 'fair', 'poor', 'impaired']).optional(),
    judgment_description: zod_1.z.string().max(500).optional(),
    suicidal_ideation: zod_1.z.enum(['denied', 'passive', 'active', 'with_plan']).optional(),
    suicidal_risk: zod_1.z.enum(['low', 'moderate', 'high', 'imminent']).optional(),
    homicidal_ideation: zod_1.z.enum(['denied', 'present', 'with_plan']).optional(),
    homicidal_risk: zod_1.z.enum(['low', 'moderate', 'high', 'imminent']).optional(),
    clinical_notes: zod_1.z.string().max(2000).optional(),
    recommendations: zod_1.z.string().max(1000).optional(),
    followup_needed: zod_1.z.boolean().optional(),
});
exports.updateMentalStatusExamSchema = exports.createMentalStatusExamSchema.partial();
exports.createMedicationHistorySchema = zod_1.z.object({
    patientId: zod_1.z.string().uuid('Invalid patient ID'),
    medicationName: zod_1.z.string().min(1, 'Medication name is required').max(200),
    genericName: zod_1.z.string().max(200).optional(),
    brandName: zod_1.z.string().max(200).optional(),
    strength: zod_1.z.string().min(1, 'Strength is required').max(100),
    dosage: zod_1.z.string().min(1, 'Dosage is required').max(100),
    frequency: zod_1.z.string().min(1, 'Frequency is required').max(100),
    route: zod_1.z.enum(['PO', 'IM', 'IV', 'SL', 'PR', 'Topical', 'Inhalation', 'Nasal', 'Ophthalmic', 'Otic']),
    indication: zod_1.z.string().min(1, 'Indication is required').max(500),
    startDate: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid start date format'),
    endDate: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid end date format').optional(),
    duration: zod_1.z.string().max(100).optional(),
    prescribedBy: zod_1.z.string().min(1, 'Prescriber name is required').max(200),
    prescriberId: zod_1.z.string().uuid().optional(),
    pharmacy: zod_1.z.string().max(200).optional(),
    sideEffects: zod_1.z.string().optional(),
    effectiveness: zod_1.z.enum(['excellent', 'good', 'fair', 'poor', 'unknown']).optional(),
    adherence: zod_1.z.enum(['excellent', 'good', 'fair', 'poor', 'unknown']).optional(),
    adherenceNotes: zod_1.z.string().max(500).optional(),
    discontinuedReason: zod_1.z.string().max(500).optional(),
    allergicReaction: zod_1.z.boolean().optional(),
    interactions: zod_1.z.string().optional(),
    monitoring: zod_1.z.string().max(500).optional(),
    prn: zod_1.z.boolean().optional(),
    notes: zod_1.z.string().max(1000).optional(),
    isActive: zod_1.z.boolean().optional(),
});
exports.updateMedicationHistorySchema = exports.createMedicationHistorySchema.partial();
exports.createNotificationSchema = zod_1.z.object({
    recipientId: zod_1.z.string().uuid(),
    type: zod_1.z.enum([
        'APPOINTMENT_REMINDER',
        'LAB_RESULT_AVAILABLE',
        'APPOINTMENT_CANCELLED',
        'APPOINTMENT_RESCHEDULED',
        'LAB_RESULT_CRITICAL',
        'SYSTEM_NOTIFICATION',
        'WELCOME',
        'PASSWORD_RESET',
        'ACCOUNT_LOCKED',
        'OTHER'
    ]),
    title: zod_1.z.string(),
    message: zod_1.z.string(),
    priority: zod_1.z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
    scheduledFor: zod_1.z.string().datetime().optional(),
    patientId: zod_1.z.string().uuid().optional(),
    appointmentId: zod_1.z.string().uuid().optional(),
    labResultId: zod_1.z.string().uuid().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
    channel: zod_1.z.enum(['IN_APP', 'EMAIL', 'SMS']).optional(),
});
exports.createPatientSchema = zod_1.z.object({
    firstName: zod_1.z.string(),
    lastName: zod_1.z.string(),
    dateOfBirth: zod_1.z.string().datetime(),
    gender: zod_1.z.enum(['MALE', 'FEMALE', 'NON_BINARY', 'PREFER_NOT_TO_SAY', 'OTHER']),
    phone: zod_1.z.string().optional(),
    email: zod_1.z.string().email().optional(),
    address: zod_1.z.object({
        street: zod_1.z.string(),
        city: zod_1.z.string(),
        state: zod_1.z.string(),
        zipCode: zod_1.z.string(),
        country: zod_1.z.string().optional(),
    }).optional(),
    occupation: zod_1.z.string().optional(),
    education: zod_1.z.enum(['ELEMENTARY', 'HIGH_SCHOOL', 'SOME_COLLEGE', 'BACHELORS', 'MASTERS', 'DOCTORATE', 'PROFESSIONAL', 'OTHER']).optional(),
    maritalStatus: zod_1.z.enum(['SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED', 'SEPARATED', 'DOMESTIC_PARTNERSHIP', 'OTHER']).optional(),
    emergencyContact: zod_1.z.object({
        name: zod_1.z.string(),
        phone: zod_1.z.string(),
        relationship: zod_1.z.string(),
        email: zod_1.z.string().email().optional(),
    }).optional(),
    insuranceInfo: zod_1.z.object({
        provider: zod_1.z.string(),
        policyNumber: zod_1.z.string(),
        groupNumber: zod_1.z.string().optional(),
        subscriberName: zod_1.z.string().optional(),
        effectiveDate: zod_1.z.string().optional(),
        expirationDate: zod_1.z.string().optional(),
    }).optional(),
    medicalHistory: zod_1.z.string().optional(),
    allergies: zod_1.z.string().optional(),
    currentMeds: zod_1.z.string().optional(),
    notes: zod_1.z.string().optional(),
});
exports.updatePatientSchema = exports.createPatientSchema.partial().extend({
    isActive: zod_1.z.boolean().optional(),
});
//# sourceMappingURL=validation.js.map