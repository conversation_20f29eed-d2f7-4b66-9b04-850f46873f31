"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationController = void 0;
const zod_1 = require("zod");
const notificationService_1 = require("@/services/notificationService");
const validation_1 = require("../utils/validation");
const querySchema = zod_1.z.object({
    page: zod_1.z.string().optional(),
    limit: zod_1.z.string().optional(),
    recipientId: zod_1.z.string().uuid().optional(),
    type: zod_1.z.string().optional(),
    status: zod_1.z.string().optional(),
    priority: zod_1.z.string().optional(),
    channel: zod_1.z.string().optional(),
    dateFrom: zod_1.z.string().optional(),
    dateTo: zod_1.z.string().optional(),
    sortBy: zod_1.z.string().optional(),
    sortOrder: zod_1.z.enum(['asc', 'desc']).optional(),
});
const appointmentReminderSchema = zod_1.z.object({
    appointmentId: zod_1.z.string().uuid('Invalid appointment ID'),
});
const labResultNotificationSchema = zod_1.z.object({
    labResultId: zod_1.z.string().uuid('Invalid lab result ID'),
});
class NotificationController {
    static async createNotification(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const validatedData = validation_1.createNotificationSchema.parse(req.body);
            const result = await notificationService_1.NotificationService.createNotification(validatedData, req.user.id);
            res.status(201).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getNotifications(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const query = querySchema.parse(req.query);
            const result = await notificationService_1.NotificationService.getNotifications(query, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getNotificationById(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const result = await notificationService_1.NotificationService.getNotificationById(req.params.id, req.user.id);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async markAsRead(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const result = await notificationService_1.NotificationService.markAsRead(req.params.id, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async markAllAsRead(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const result = await notificationService_1.NotificationService.markAllAsRead(req.user.id);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async deleteNotification(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const result = await notificationService_1.NotificationService.deleteNotification(req.params.id, req.user.id);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async createAppointmentReminders(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { appointmentId } = appointmentReminderSchema.parse(req.body);
            const result = await notificationService_1.NotificationService.createAppointmentReminders(appointmentId, req.user.id);
            res.status(201).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async sendLabResultNotification(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { labResultId } = labResultNotificationSchema.parse(req.body);
            const result = await notificationService_1.NotificationService.sendLabResultNotification(labResultId, req.user.id);
            res.status(201).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async processScheduledNotifications(req, res, next) {
        try {
            const result = await notificationService_1.NotificationService.processScheduledNotifications();
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getNotificationStats(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const result = await notificationService_1.NotificationService.getNotificationStats(req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
}
exports.NotificationController = NotificationController;
//# sourceMappingURL=notificationController.js.map