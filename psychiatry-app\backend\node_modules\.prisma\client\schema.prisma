generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id                            String                 @id @default(uuid())
  username                      String                 @unique
  email                         String                 @unique
  password                      String
  role                          String                 @default("CLINICIAN")
  firstName                     String
  lastName                      String
  isActive                      Boolean                @default(true)
  lastLogin                     DateTime?
  loginAttempts                 Int                    @default(0)
  lockedUntil                   DateTime?
  createdAt                     DateTime               @default(now())
  updatedAt                     DateTime               @updatedAt
  phone                         String?
  createdAppointments           Appointment[]          @relation("AppointmentsCreatedBy")
  providedAppointments          Appointment[]          @relation("AppointmentsProvidedBy")
  auditLogs                     AuditLog[]
  createdLabResults             LabResult[]            @relation("LabResultCreator")
  notifications                 Notification[]         @relation("NotificationRecipient")
  assessments                   PatientAssessment[]
  createdPatients               Patient[]              @relation("PatientCreatedBy")
  providedRecurringAppointments RecurringAppointment[] @relation("RecurringAppointmentProvider")
  refreshTokens                 RefreshToken[]

  @@index([email])
  @@index([username])
  @@index([role])
  @@index([isActive])
  @@index([lastLogin])
  @@map("users")
}

model RefreshToken {
  id        String   @id @default(uuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  isRevoked Boolean  @default(false)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([expiresAt])
  @@index([isRevoked])
  @@map("refresh_tokens")
}

model Patient {
  id                    String                 @id @default(uuid())
  patientId             String                 @unique
  firstName             String
  lastName              String
  dateOfBirth           DateTime
  gender                String
  phone                 String?
  email                 String?
  address               String?
  occupation            String?
  education             String?
  maritalStatus         String?
  emergencyContact      String?
  insuranceInfo         String?
  medicalHistory        String?
  allergies             String?
  currentMeds           String?
  notes                 String?
  isActive              Boolean                @default(true)
  isDeleted             Boolean                @default(false)
  deletedAt             DateTime?
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  createdBy             String
  appointments          Appointment[]
  auditLogs             AuditLog[]
  labResults            LabResult[]
  notifications         Notification[]
  assessments           PatientAssessment[]
  creator               User                   @relation("PatientCreatedBy", fields: [createdBy], references: [id])
  recurringAppointments RecurringAppointment[]

  @@index([lastName, firstName])
  @@index([dateOfBirth])
  @@index([gender])
  @@index([isActive])
  @@index([isDeleted])
  @@index([createdAt])
  @@index([createdBy])
  @@map("patients")
}

model LabResult {
  id            String         @id @default(uuid())
  patientId     String
  testType      String
  testDate      DateTime
  orderedBy     String
  labName       String?
  results       String
  normalRanges  String?
  flags         String?
  notes         String?
  status        String         @default("COMPLETED")
  createdBy     String
  isDeleted     Boolean        @default(false)
  deletedAt     DateTime?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  auditLogs     AuditLog[]
  creator       User           @relation("LabResultCreator", fields: [createdBy], references: [id])
  patient       Patient        @relation(fields: [patientId], references: [id], onDelete: Cascade)
  notifications Notification[]

  @@index([patientId])
  @@index([testType])
  @@index([testDate])
  @@index([status])
  @@index([createdBy])
  @@index([isDeleted])
  @@index([createdAt])
  @@index([patientId, testDate])
  @@map("lab_results")
}

model Appointment {
  id                     String                @id @default(uuid())
  patientId              String
  providerId             String?
  recurringAppointmentId String?
  date                   DateTime
  endTime                DateTime?
  duration               Int
  type                   String
  status                 String                @default("SCHEDULED")
  title                  String?
  description            String?
  location               String?
  isVirtual              Boolean               @default(false)
  virtualMeetingUrl      String?
  notes                  String?
  isDeleted              Boolean               @default(false)
  deletedAt              DateTime?
  createdBy              String?
  createdAt              DateTime              @default(now())
  updatedAt              DateTime              @updatedAt
  recurringAppointment   RecurringAppointment? @relation(fields: [recurringAppointmentId], references: [id])
  creator                User?                 @relation("AppointmentsCreatedBy", fields: [createdBy], references: [id])
  provider               User?                 @relation("AppointmentsProvidedBy", fields: [providerId], references: [id])
  patient                Patient               @relation(fields: [patientId], references: [id], onDelete: Cascade)
  auditLogs              AuditLog[]
  notifications          Notification[]

  @@index([patientId])
  @@index([providerId])
  @@index([date])
  @@index([status])
  @@index([type])
  @@index([isDeleted])
  @@index([patientId, date])
  @@map("appointments")
}

model Notification {
  id            String       @id @default(uuid())
  recipientId   String
  type          String
  title         String
  message       String
  priority      String       @default("MEDIUM")
  channel       String       @default("IN_APP")
  status        String       @default("PENDING")
  isRead        Boolean      @default(false)
  isProcessed   Boolean      @default(false)
  scheduledFor  DateTime?
  processedAt   DateTime?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  patientId     String?
  appointmentId String?
  labResultId   String?
  labResult     LabResult?   @relation(fields: [labResultId], references: [id])
  appointment   Appointment? @relation(fields: [appointmentId], references: [id])
  patient       Patient?     @relation(fields: [patientId], references: [id])
  recipient     User         @relation("NotificationRecipient", fields: [recipientId], references: [id])

  @@index([recipientId])
  @@index([type])
  @@index([channel])
  @@index([status])
  @@index([isRead])
  @@index([isProcessed])
  @@index([scheduledFor])
  @@index([createdAt])
  @@index([recipientId, isRead])
  @@map("notifications")
}

model RecurringAppointment {
  id             String        @id @default(uuid())
  patientId      String
  providerId     String
  startDate      DateTime
  endDate        DateTime?
  duration       Int
  type           String
  frequency      String
  interval       Int           @default(1)
  dayOfWeek      Int?
  dayOfMonth     Int?
  timeSlot       String
  notes          String?
  maxOccurrences Int?
  isActive       Boolean       @default(true)
  isDeleted      Boolean       @default(false)
  deletedAt      DateTime?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  appointments   Appointment[]
  provider       User          @relation("RecurringAppointmentProvider", fields: [providerId], references: [id])
  patient        Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@index([patientId])
  @@index([providerId])
  @@index([startDate])
  @@index([isActive])
  @@index([isDeleted])
  @@index([frequency])
  @@map("recurring_appointments")
}

model AuditLog {
  id            String       @id @default(uuid())
  userId        String
  action        String
  entityType    String
  entityId      String
  oldValues     String?
  newValues     String?
  ipAddress     String?
  userAgent     String?
  timestamp     DateTime     @default(now())
  patientId     String?
  labResultId   String?
  appointmentId String?
  appointment   Appointment? @relation(fields: [appointmentId], references: [id])
  labResult     LabResult?   @relation(fields: [labResultId], references: [id])
  patient       Patient?     @relation(fields: [patientId], references: [id])
  user          User         @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([action])
  @@index([entityType])
  @@index([entityId])
  @@index([timestamp])
  @@index([patientId])
  @@index([userId, timestamp])
  @@map("audit_logs")
}

model PatientAssessment {
  id             String    @id @default(uuid())
  patientId      String
  assessorId     String
  sessionDate    DateTime
  assessmentData String
  status         String
  duration       Int
  isDeleted      Boolean   @default(false)
  deletedAt      DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  assessor       User      @relation(fields: [assessorId], references: [id])
  patient        Patient   @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@index([patientId])
  @@index([assessorId])
  @@index([sessionDate])
  @@index([status])
  @@index([isDeleted])
  @@index([createdAt])
  @@map("patient_assessments")
}
