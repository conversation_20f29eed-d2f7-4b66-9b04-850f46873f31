import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  Calendar, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  User,
  FileText,
  BarChart3,
  Eye
} from 'lucide-react';
import api from '../../lib/api';

interface TestHistoryProps {
  patientId: string;
}

interface PsychTestResult {
  id: string;
  testName: string;
  testCategory: string;
  administeredBy: string;
  administeredDate: string;
  completionTime?: number;
  totalScore: number;
  severity?: string;
  clinicalRange?: string;
  interpretation?: string;
  recommendations?: string;
  validity?: string;
  notes?: string;
  followUpRequired: boolean;
  baselineTest: boolean;
  patient?: {
    id: string;
    patientId: string;
    firstName: string;
    lastName: string;
  };
}

export const TestHistory: React.FC<TestHistoryProps> = ({ patientId }) => {
  const [testHistory, setTestHistory] = useState<PsychTestResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTest, setSelectedTest] = useState<PsychTestResult | null>(null);
  const [filter, setFilter] = useState<'all' | 'depression' | 'anxiety' | 'cognitive'>('all');

  useEffect(() => {
    fetchTestHistory();
  }, [patientId]);

  const fetchTestHistory = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.get(`/api/psych-tests/patient/${patientId}`);
      
      if (response.data.success) {
        setTestHistory(response.data.data);
      } else {
        setError('Failed to fetch test history');
      }
    } catch (err) {
      console.error('Error fetching test history:', err);
      setError('Failed to load test history');
    } finally {
      setLoading(false);
    }
  };

  const filteredTests = testHistory.filter(test => {
    if (filter === 'all') return true;
    return test.testCategory === filter;
  });

  const getSeverityColor = (severity?: string) => {
    switch (severity?.toLowerCase()) {
      case 'minimal': return 'bg-green-100 text-green-800';
      case 'mild': return 'bg-yellow-100 text-yellow-800';
      case 'moderate': return 'bg-orange-100 text-orange-800';
      case 'severe': return 'bg-red-100 text-red-800';
      case 'extreme': return 'bg-red-200 text-red-900';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getClinicalRangeIcon = (clinicalRange?: string) => {
    switch (clinicalRange?.toLowerCase()) {
      case 'normal': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'borderline': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'clinical': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getTestCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'depression': return <TrendingDown className="h-4 w-4 text-blue-600" />;
      case 'anxiety': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'cognitive': return <BarChart3 className="h-4 w-4 text-purple-600" />;
      case 'ocd': return <TrendingUp className="h-4 w-4 text-indigo-600" />;
      case 'psychosis': return <Eye className="h-4 w-4 text-red-600" />;
      default: return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const calculateTrend = (testName: string): 'improving' | 'stable' | 'worsening' | 'insufficient' => {
    const sameTests = testHistory
      .filter(test => test.testName === testName)
      .sort((a, b) => new Date(a.administeredDate).getTime() - new Date(b.administeredDate).getTime());
    
    if (sameTests.length < 2) return 'insufficient';
    
    const latest = sameTests[sameTests.length - 1];
    const previous = sameTests[sameTests.length - 2];
    
    if (latest.totalScore < previous.totalScore) return 'improving';
    if (latest.totalScore > previous.totalScore) return 'worsening';
    return 'stable';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return <TrendingDown className="h-4 w-4 text-green-600" />;
      case 'worsening': return <TrendingUp className="h-4 w-4 text-red-600" />;
      case 'stable': return <TrendingUp className="h-4 w-4 text-blue-600 rotate-90" />;
      default: return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          {[1, 2, 3].map(i => (
            <div key={i} className="h-24 bg-gray-200 rounded mb-4"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <AlertTriangle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Test History</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={fetchTestHistory}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Test History</h2>
          <p className="text-gray-600">Chronological record of psychological assessments</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <FileText className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700">Filter by category:</span>
          {(['all', 'depression', 'anxiety', 'cognitive'] as const).map((filterOption) => (
            <Button
              key={filterOption}
              variant={filter === filterOption ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter(filterOption)}
            >
              {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
            </Button>
          ))}
        </div>
        <div className="text-sm text-gray-600">
          {filteredTests.length} test{filteredTests.length !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Test History Timeline */}
      <div className="space-y-4">
        {filteredTests.map((test, index) => {
          const trend = calculateTrend(test.testName);
          
          return (
            <Card key={test.id} className="hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => setSelectedTest(test)}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      {getTestCategoryIcon(test.testCategory)}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{test.testName}</h3>
                        {test.baselineTest && (
                          <Badge variant="outline" className="text-xs">
                            Baseline
                          </Badge>
                        )}
                        {test.followUpRequired && (
                          <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                            Follow-up Required
                          </Badge>
                        )}
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span>{new Date(test.administeredDate).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <User className="h-4 w-4 text-gray-400" />
                          <span>{test.administeredBy}</span>
                        </div>
                        {test.completionTime && (
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4 text-gray-400" />
                            <span>{test.completionTime} min</span>
                          </div>
                        )}
                        <div className="flex items-center space-x-1">
                          {getTrendIcon(trend)}
                          <span className="capitalize">{trend}</span>
                        </div>
                      </div>
                      
                      {test.interpretation && (
                        <p className="text-sm text-gray-600 mb-2">{test.interpretation}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex flex-col items-end space-y-2">
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">{test.totalScore}</div>
                      <div className="text-xs text-gray-500">Total Score</div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {test.severity && (
                        <Badge className={getSeverityColor(test.severity)}>
                          {test.severity}
                        </Badge>
                      )}
                      {test.clinicalRange && (
                        <div className="flex items-center space-x-1">
                          {getClinicalRangeIcon(test.clinicalRange)}
                          <span className="text-xs text-gray-600 capitalize">{test.clinicalRange}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty state */}
      {filteredTests.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No test history found</h3>
            <p className="text-gray-600 mb-4">
              {filter === 'all' 
                ? 'This patient has no psychological test history yet.' 
                : `No ${filter} tests found.`}
            </p>
            <Button>
              Start First Assessment
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Test Detail Modal */}
      {selectedTest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>{selectedTest.testName} - Detailed Results</CardTitle>
                <Button variant="outline" size="sm" onClick={() => setSelectedTest(null)}>
                  Close
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Date:</span>
                  <p>{new Date(selectedTest.administeredDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Administered by:</span>
                  <p>{selectedTest.administeredBy}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Total Score:</span>
                  <p className="text-lg font-bold">{selectedTest.totalScore}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Severity:</span>
                  <Badge className={getSeverityColor(selectedTest.severity)}>
                    {selectedTest.severity}
                  </Badge>
                </div>
              </div>
              
              {selectedTest.interpretation && (
                <div>
                  <span className="font-medium text-gray-700">Interpretation:</span>
                  <p className="text-sm text-gray-600 mt-1">{selectedTest.interpretation}</p>
                </div>
              )}
              
              {selectedTest.recommendations && (
                <div>
                  <span className="font-medium text-gray-700">Recommendations:</span>
                  <p className="text-sm text-gray-600 mt-1">{selectedTest.recommendations}</p>
                </div>
              )}
              
              {selectedTest.notes && (
                <div>
                  <span className="font-medium text-gray-700">Notes:</span>
                  <p className="text-sm text-gray-600 mt-1">{selectedTest.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
