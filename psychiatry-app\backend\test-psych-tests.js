const axios = require('axios');

const BASE_URL = 'http://localhost:3002';

async function testPsychTests() {
  try {
    console.log('🔍 Testing Psychological Tests API...\n');

    // Step 1: Login
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      username: 'admin',
      password: 'admin123'
    });

    const token = loginResponse.data.data.accessToken;
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Step 2: Get patients
    const patientsResponse = await axios.get(`${BASE_URL}/api/patients`, { headers });
    const patient = patientsResponse.data.data[0];
    console.log('✅ Using patient:', patient.patientId, '-', patient.firstName, patient.lastName);

    // Step 3: Create a PHQ-9 test result
    console.log('\nTesting create PHQ-9 test result...');
    
    const phq9Data = {
      patientId: patient.id,
      testName: 'PHQ-9',
      testCategory: 'depression',
      version: '1.0',
      administeredBy: 'Dr<PERSON> <PERSON>',
      administeredDate: new Date().toISOString(),
      completionTime: 15,
      location: 'office',
      rawScore: 12,
      totalScore: 12,
      severity: 'moderate',
      clinicalRange: 'clinical',
      interpretation: 'Moderate depression symptoms present. Patient reports significant mood disturbance.',
      recommendations: 'Consider therapy and medication evaluation',
      responses: JSON.stringify({
        q1: 2, q2: 1, q3: 2, q4: 1, q5: 2, q6: 1, q7: 2, q8: 1, q9: 0
      }),
      validity: 'valid',
      notes: 'Patient was cooperative and engaged during assessment',
      followUpRequired: true,
      baselineTest: true
    };

    try {
      const createResponse = await axios.post(`${BASE_URL}/api/psych-tests`, phq9Data, { headers });
      console.log('✅ PHQ-9 test created:', createResponse.status);
      console.log('Created test ID:', createResponse.data.data.id);
    } catch (error) {
      console.log('❌ Failed to create PHQ-9 test');
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data);
    }

    // Step 4: Create a GAD-7 test result
    console.log('\nTesting create GAD-7 test result...');
    
    const gad7Data = {
      patientId: patient.id,
      testName: 'GAD-7',
      testCategory: 'anxiety',
      version: '1.0',
      administeredBy: 'Dr. Smith',
      administeredDate: new Date().toISOString(),
      completionTime: 10,
      location: 'office',
      rawScore: 8,
      totalScore: 8,
      severity: 'mild',
      clinicalRange: 'borderline',
      interpretation: 'Mild anxiety symptoms present. Some worry and tension reported.',
      recommendations: 'Monitor symptoms, consider relaxation techniques',
      responses: JSON.stringify({
        q1: 1, q2: 1, q3: 1, q4: 1, q5: 1, q6: 1, q7: 2
      }),
      validity: 'valid',
      notes: 'Patient reported recent stress at work',
      followUpRequired: false,
      baselineTest: false
    };

    try {
      const createResponse = await axios.post(`${BASE_URL}/api/psych-tests`, gad7Data, { headers });
      console.log('✅ GAD-7 test created:', createResponse.status);
      console.log('Created test ID:', createResponse.data.data.id);
    } catch (error) {
      console.log('❌ Failed to create GAD-7 test');
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data);
    }

    // Step 5: Get psychological tests for patient
    console.log('\nTesting get psychological tests by patient...');
    
    try {
      const getResponse = await axios.get(`${BASE_URL}/api/psych-tests/patient/${patient.id}`, { headers });
      console.log('✅ Psychological tests retrieved:', getResponse.status);
      console.log('Number of tests:', getResponse.data.data.length);
      
      if (getResponse.data.data.length > 0) {
        console.log('First test:', JSON.stringify(getResponse.data.data[0], null, 2));
      }
    } catch (error) {
      console.log('❌ Failed to get psychological tests');
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data);
    }

    // Step 6: Get all psychological tests
    console.log('\nTesting get all psychological tests...');
    
    try {
      const getAllResponse = await axios.get(`${BASE_URL}/api/psych-tests`, { headers });
      console.log('✅ All psychological tests retrieved:', getAllResponse.status);
      console.log('Total tests:', getAllResponse.data.data.length);
    } catch (error) {
      console.log('❌ Failed to get all psychological tests');
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data);
    }

    console.log('\n🎉 Psychological tests API test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testPsychTests();
