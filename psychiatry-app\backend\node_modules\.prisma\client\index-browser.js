
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  email: 'email',
  password: 'password',
  role: 'role',
  firstName: 'firstName',
  lastName: 'lastName',
  isActive: 'isActive',
  lastLogin: 'lastLogin',
  loginAttempts: 'loginAttempts',
  lockedUntil: 'lockedUntil',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  phone: 'phone'
};

exports.Prisma.RefreshTokenScalarFieldEnum = {
  id: 'id',
  token: 'token',
  userId: 'userId',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  isRevoked: 'isRevoked'
};

exports.Prisma.PatientScalarFieldEnum = {
  id: 'id',
  patientId: 'patientId',
  firstName: 'firstName',
  lastName: 'lastName',
  dateOfBirth: 'dateOfBirth',
  gender: 'gender',
  phone: 'phone',
  email: 'email',
  address: 'address',
  occupation: 'occupation',
  education: 'education',
  maritalStatus: 'maritalStatus',
  emergencyContact: 'emergencyContact',
  insuranceInfo: 'insuranceInfo',
  medicalHistory: 'medicalHistory',
  allergies: 'allergies',
  currentMeds: 'currentMeds',
  notes: 'notes',
  isActive: 'isActive',
  isDeleted: 'isDeleted',
  deletedAt: 'deletedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy'
};

exports.Prisma.LabResultScalarFieldEnum = {
  id: 'id',
  patientId: 'patientId',
  testName: 'testName',
  testCategory: 'testCategory',
  testType: 'testType',
  value: 'value',
  numericValue: 'numericValue',
  unit: 'unit',
  referenceRange: 'referenceRange',
  referenceMin: 'referenceMin',
  referenceMax: 'referenceMax',
  status: 'status',
  flagged: 'flagged',
  testDate: 'testDate',
  resultDate: 'resultDate',
  orderedBy: 'orderedBy',
  reviewedBy: 'reviewedBy',
  laboratoryId: 'laboratoryId',
  specimenType: 'specimenType',
  notes: 'notes',
  criticalValue: 'criticalValue',
  deltaCheck: 'deltaCheck',
  createdBy: 'createdBy',
  isDeleted: 'isDeleted',
  deletedAt: 'deletedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AppointmentScalarFieldEnum = {
  id: 'id',
  patientId: 'patientId',
  providerId: 'providerId',
  recurringAppointmentId: 'recurringAppointmentId',
  date: 'date',
  endTime: 'endTime',
  duration: 'duration',
  type: 'type',
  status: 'status',
  title: 'title',
  description: 'description',
  location: 'location',
  isVirtual: 'isVirtual',
  virtualMeetingUrl: 'virtualMeetingUrl',
  notes: 'notes',
  isDeleted: 'isDeleted',
  deletedAt: 'deletedAt',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  recipientId: 'recipientId',
  type: 'type',
  title: 'title',
  message: 'message',
  priority: 'priority',
  channel: 'channel',
  status: 'status',
  isRead: 'isRead',
  isProcessed: 'isProcessed',
  scheduledFor: 'scheduledFor',
  processedAt: 'processedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  patientId: 'patientId',
  appointmentId: 'appointmentId',
  labResultId: 'labResultId'
};

exports.Prisma.RecurringAppointmentScalarFieldEnum = {
  id: 'id',
  patientId: 'patientId',
  providerId: 'providerId',
  startDate: 'startDate',
  endDate: 'endDate',
  duration: 'duration',
  type: 'type',
  frequency: 'frequency',
  interval: 'interval',
  dayOfWeek: 'dayOfWeek',
  dayOfMonth: 'dayOfMonth',
  timeSlot: 'timeSlot',
  notes: 'notes',
  maxOccurrences: 'maxOccurrences',
  isActive: 'isActive',
  isDeleted: 'isDeleted',
  deletedAt: 'deletedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  entityType: 'entityType',
  entityId: 'entityId',
  oldValues: 'oldValues',
  newValues: 'newValues',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  timestamp: 'timestamp',
  patientId: 'patientId',
  labResultId: 'labResultId',
  appointmentId: 'appointmentId'
};

exports.Prisma.PatientAssessmentScalarFieldEnum = {
  id: 'id',
  patientId: 'patientId',
  assessorId: 'assessorId',
  sessionDate: 'sessionDate',
  assessmentData: 'assessmentData',
  status: 'status',
  duration: 'duration',
  isDeleted: 'isDeleted',
  deletedAt: 'deletedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PsychTestScalarFieldEnum = {
  id: 'id',
  patientId: 'patientId',
  testName: 'testName',
  testCategory: 'testCategory',
  version: 'version',
  administeredBy: 'administeredBy',
  administeredDate: 'administeredDate',
  completionTime: 'completionTime',
  location: 'location',
  rawScore: 'rawScore',
  totalScore: 'totalScore',
  subscaleScores: 'subscaleScores',
  scaledScore: 'scaledScore',
  percentile: 'percentile',
  tScore: 'tScore',
  zScore: 'zScore',
  severity: 'severity',
  clinicalRange: 'clinicalRange',
  interpretation: 'interpretation',
  recommendations: 'recommendations',
  responses: 'responses',
  validity: 'validity',
  validityIndices: 'validityIndices',
  notes: 'notes',
  followUpDate: 'followUpDate',
  followUpRequired: 'followUpRequired',
  batteryId: 'batteryId',
  sessionNumber: 'sessionNumber',
  baselineTest: 'baselineTest',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MentalStatusExamScalarFieldEnum = {
  id: 'id',
  patientId: 'patientId',
  examDate: 'examDate',
  examinerId: 'examinerId',
  appearance_grooming: 'appearance_grooming',
  appearance_dress: 'appearance_dress',
  appearance_hygiene: 'appearance_hygiene',
  behavior_eye_contact: 'behavior_eye_contact',
  behavior_motor: 'behavior_motor',
  behavior_cooperation: 'behavior_cooperation',
  speech_rate: 'speech_rate',
  speech_volume: 'speech_volume',
  speech_tone: 'speech_tone',
  speech_fluency: 'speech_fluency',
  mood_reported: 'mood_reported',
  mood_observed: 'mood_observed',
  affect_type: 'affect_type',
  affect_range: 'affect_range',
  affect_appropriateness: 'affect_appropriateness',
  thought_process: 'thought_process',
  thought_organization: 'thought_organization',
  thought_flow: 'thought_flow',
  thought_content: 'thought_content',
  delusions: 'delusions',
  delusion_type: 'delusion_type',
  obsessions: 'obsessions',
  compulsions: 'compulsions',
  phobias: 'phobias',
  hallucinations: 'hallucinations',
  hallucination_type: 'hallucination_type',
  illusions: 'illusions',
  depersonalization: 'depersonalization',
  derealization: 'derealization',
  orientation_person: 'orientation_person',
  orientation_place: 'orientation_place',
  orientation_time: 'orientation_time',
  orientation_situation: 'orientation_situation',
  attention_span: 'attention_span',
  concentration: 'concentration',
  memory_immediate: 'memory_immediate',
  memory_recent: 'memory_recent',
  memory_remote: 'memory_remote',
  abstract_thinking: 'abstract_thinking',
  insight_level: 'insight_level',
  insight_description: 'insight_description',
  judgment_level: 'judgment_level',
  judgment_description: 'judgment_description',
  suicidal_ideation: 'suicidal_ideation',
  suicidal_risk: 'suicidal_risk',
  homicidal_ideation: 'homicidal_ideation',
  homicidal_risk: 'homicidal_risk',
  clinical_notes: 'clinical_notes',
  recommendations: 'recommendations',
  followup_needed: 'followup_needed',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MedicationHistoryScalarFieldEnum = {
  id: 'id',
  patientId: 'patientId',
  medicationName: 'medicationName',
  genericName: 'genericName',
  brandName: 'brandName',
  strength: 'strength',
  dosage: 'dosage',
  frequency: 'frequency',
  route: 'route',
  indication: 'indication',
  startDate: 'startDate',
  endDate: 'endDate',
  duration: 'duration',
  prescribedBy: 'prescribedBy',
  prescriberId: 'prescriberId',
  pharmacy: 'pharmacy',
  sideEffects: 'sideEffects',
  effectiveness: 'effectiveness',
  adherence: 'adherence',
  adherenceNotes: 'adherenceNotes',
  discontinuedReason: 'discontinuedReason',
  allergicReaction: 'allergicReaction',
  interactions: 'interactions',
  monitoring: 'monitoring',
  prn: 'prn',
  notes: 'notes',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  User: 'User',
  RefreshToken: 'RefreshToken',
  Patient: 'Patient',
  LabResult: 'LabResult',
  Appointment: 'Appointment',
  Notification: 'Notification',
  RecurringAppointment: 'RecurringAppointment',
  AuditLog: 'AuditLog',
  PatientAssessment: 'PatientAssessment',
  PsychTest: 'PsychTest',
  MentalStatusExam: 'MentalStatusExam',
  MedicationHistory: 'MedicationHistory'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
