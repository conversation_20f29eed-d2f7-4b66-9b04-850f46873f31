"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.disconnectDatabase = exports.checkDatabaseConnection = exports.prisma = void 0;
const client_1 = require("@prisma/client");
const logger_1 = require("./logger");
class DatabaseClient extends client_1.PrismaClient {
    constructor() {
        super({
            log: [
                {
                    emit: 'event',
                    level: 'query',
                },
                {
                    emit: 'event',
                    level: 'error',
                },
                {
                    emit: 'event',
                    level: 'info',
                },
                {
                    emit: 'event',
                    level: 'warn',
                },
            ],
        });
    }
    async loggedQuery(operation, queryFn, context) {
        const start = Date.now();
        logger_1.logger.info('Database operation started', {
            operation,
            context,
            timestamp: new Date().toISOString(),
        });
        try {
            const result = await queryFn();
            const duration = Date.now() - start;
            logger_1.logger.info('Database operation completed', {
                operation,
                duration: `${duration}ms`,
                context,
                timestamp: new Date().toISOString(),
            });
            return result;
        }
        catch (error) {
            const duration = Date.now() - start;
            logger_1.logger.error('Database operation failed', {
                operation,
                duration: `${duration}ms`,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
                context,
                timestamp: new Date().toISOString(),
            });
            throw error;
        }
    }
}
exports.prisma = new DatabaseClient();
const checkDatabaseConnection = async () => {
    try {
        await exports.prisma.$queryRaw `SELECT 1`;
        logger_1.logger.info('Database connection healthy');
        return true;
    }
    catch (error) {
        logger_1.logger.error('Database connection failed', {
            error: error instanceof Error ? error.message : 'Unknown error',
        });
        return false;
    }
};
exports.checkDatabaseConnection = checkDatabaseConnection;
const disconnectDatabase = async () => {
    try {
        await exports.prisma.$disconnect();
        logger_1.logger.info('Database disconnected gracefully');
    }
    catch (error) {
        logger_1.logger.error('Database disconnect failed', {
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.disconnectDatabase = disconnectDatabase;
//# sourceMappingURL=database.js.map