"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const mentalStatusExamController_1 = require("../controllers/mentalStatusExamController");
const auth_1 = require("../middleware/auth");
const auth_2 = require("../middleware/auth");
const asyncHandler_1 = require("../utils/asyncHandler");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/', (0, asyncHandler_1.asyncHandler)(mentalStatusExamController_1.MentalStatusExamController.getAllMentalStatusExams));
router.post('/', (0, auth_2.authorize)(['ADMIN', 'CLINICIAN']), (0, asyncHandler_1.asyncHandler)(mentalStatusExamController_1.MentalStatusExamController.createMentalStatusExam));
router.get('/:id', (0, asyncHandler_1.asyncHandler)(mentalStatusExamController_1.MentalStatusExamController.getMentalStatusExamById));
router.put('/:id', (0, auth_2.authorize)(['ADMIN', 'CLINICIAN']), (0, asyncHandler_1.asyncHandler)(mentalStatusExamController_1.MentalStatusExamController.updateMentalStatusExam));
router.delete('/:id', (0, auth_2.authorize)(['ADMIN', 'CLINICIAN']), (0, asyncHandler_1.asyncHandler)(mentalStatusExamController_1.MentalStatusExamController.deleteMentalStatusExam));
router.get('/patient/:patientId', (0, asyncHandler_1.asyncHandler)(mentalStatusExamController_1.MentalStatusExamController.getMentalStatusExamsByPatient));
exports.default = router;
//# sourceMappingURL=mentalStatusExams.js.map