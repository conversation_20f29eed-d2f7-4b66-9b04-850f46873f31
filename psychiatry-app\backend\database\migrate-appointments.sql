-- Migration script for appointments table
-- Adding providerId column and updating existing records

-- Check if providerId column exists, if not add it
PRAGMA table_info(appointments);
ALTER TABLE appointments ADD COLUMN providerId TEXT;

-- Update existing records with a default providerId
-- First, find an admin user to use as default provider
UPDATE appointments 
SET providerId = (SELECT id FROM users WHERE role = 'ADMIN' LIMIT 1) 
WHERE providerId IS NULL;

-- If you need to view the updated records
-- SELECT id, patientId, providerId, date FROM appointments LIMIT 10;

-- Verify the number of records updated
SELECT COUNT(*) AS updatedRecords FROM appointments WHERE providerId IS NOT NULL; 