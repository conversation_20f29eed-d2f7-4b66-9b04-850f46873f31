"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const appointmentController_1 = require("../controllers/appointmentController");
const auth_1 = require("../middleware/auth");
const asyncHandler_1 = require("../utils/asyncHandler");
const router = express_1.default.Router();
router.use(auth_1.authenticate);
router.get('/stats', (0, asyncHandler_1.asyncHandler)(appointmentController_1.AppointmentController.getAppointmentStats));
router.get('/providers/:providerId/availability', (0, asyncHandler_1.asyncHandler)(appointmentController_1.AppointmentController.getProviderAvailability));
router.get('/', (0, asyncHandler_1.asyncHandler)(appointmentController_1.AppointmentController.getAppointments));
router.post('/', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN', 'STAFF']), (0, asyncHandler_1.asyncHandler)(appointmentController_1.AppointmentController.createAppointment));
router.get('/:id', (0, asyncHandler_1.asyncHandler)(appointmentController_1.AppointmentController.getAppointmentById));
router.put('/:id', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN', 'STAFF']), (0, asyncHandler_1.asyncHandler)(appointmentController_1.AppointmentController.updateAppointment));
router.post('/:id/cancel', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN', 'STAFF']), (0, asyncHandler_1.asyncHandler)(appointmentController_1.AppointmentController.cancelAppointment));
exports.default = router;
//# sourceMappingURL=appointments.js.map